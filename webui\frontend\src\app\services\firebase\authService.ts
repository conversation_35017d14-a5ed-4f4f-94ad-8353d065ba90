import {
  GoogleAuthProvider,
  signInWithPopup,
  onAuthStateChanged,
  signOut,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  User
} from "firebase/auth";
import { doc, getDoc, setDoc, serverTimestamp } from "firebase/firestore";
import { auth, db } from "./firebaseConfig";

// Initialize auth state listener
onAuthStateChanged(auth, async (user: User | null) => {
  if (user) {
    await handleUserLoggedIn(user);
  } else {
    console.log("User signed out");
  }
});

// Function to handle user data in Firestore when logged in
const handleUserLoggedIn = async (user: User) => {
  const uid = user.uid;
  const userRef = doc(db, "users", uid);
  const userDocSnap = await getDoc(userRef);

  if (!userDocSnap.exists()) {
    try {
      // Create a new user document if it doesn't exist
      await setDoc(userRef, {
        uid: uid,
        email: user.email,
        displayName: user.displayName || '',
        photoURL: user.photoURL || '',
        createdAt: serverTimestamp(),
        lastLogin: serverTimestamp(),
      });
    } catch (error) {
      console.error("Error adding user to Firestore:", error);
    }
  } else {
    // Update last login time
    try {
      await setDoc(userRef, {
        lastLogin: serverTimestamp(),
      }, { merge: true });
    } catch (error) {
      console.error("Error updating last login time:", error);
    }
  }
};

// Helper function to format Firebase auth errors
const formatAuthError = (error: any): string => {
  const errorCode = error.code;

  switch (errorCode) {
    case 'auth/email-already-in-use':
      return 'This email is already in use. Please try signing in instead.';
    case 'auth/invalid-email':
      return 'Invalid email address format.';
    case 'auth/user-disabled':
      return 'This account has been disabled. Please contact support.';
    case 'auth/user-not-found':
      return 'No account found with this email. Please sign up first.';
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again.';
    case 'auth/weak-password':
      return 'Password is too weak. Please use a stronger password.';
    case 'auth/popup-closed-by-user':
      return 'Sign in was cancelled. Please try again.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your internet connection.';
    case 'auth/too-many-requests':
      return 'Too many unsuccessful login attempts. Please try again later.';
    default:
      return error.message || 'An error occurred during authentication. Please try again.';
  }
};

// Sign in with Google
export const signInWithGoogle = async (): Promise<void> => {
  const provider = new GoogleAuthProvider();
  provider.setCustomParameters({ prompt: 'select_account' });

  try {
    await signInWithPopup(auth, provider);
  } catch (error) {
    console.error("Error signing in with Google:", error);
    throw new Error(formatAuthError(error));
  }
};

// Sign out
export const signOutUser = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error("Error signing out:", error);
    throw new Error(formatAuthError(error));
  }
};

// Sign up with email and password
export const signUpWithEmail = async (
  email: string,
  password: string
): Promise<void> => {
  try {
    await createUserWithEmailAndPassword(auth, email, password);
  } catch (error) {
    console.error("Error signing up:", error);
    throw new Error(formatAuthError(error));
  }
};

// Sign in with email and password
export const signInWithEmail = async (
  email: string,
  password: string
): Promise<void> => {
  try {
    await signInWithEmailAndPassword(auth, email, password);
  } catch (error) {
    console.error("Error signing in:", error);
    throw new Error(formatAuthError(error));
  }
};

// Get current user
export const getCurrentUser = (): User | null => {
  return auth.currentUser;
};

// Subscribe to auth state changes
export const subscribeToAuthChanges = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};
