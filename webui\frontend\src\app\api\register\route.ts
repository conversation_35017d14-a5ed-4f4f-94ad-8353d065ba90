import { NextResponse } from 'next/server'

// This is a simple in-memory store for demo purposes
// In a real app, you would use a database
const users = new Map<string, { id: string, name: string, email: string, password: string }>()

// Add a demo user
users.set("<EMAIL>", {
  id: "1",
  name: "Demo User",
  email: "<EMAIL>",
  password: "password123" // In a real app, this would be hashed
})

export async function POST(request: Request) {
  try {
    const { name, email, password } = await request.json()

    // Basic validation
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters' },
        { status: 400 }
      )
    }

    // Check if user already exists
    if (users.has(email)) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 409 }
      )
    }

    // Generate a unique ID (in a real app, this would be handled by the database)
    const id = (users.size + 1).toString()

    // Create the user
    users.set(email, {
      id,
      name,
      email,
      password // In a real app, this would be hashed
    })

    return NextResponse.json(
      { success: true, message: 'User registered successfully' },
      { status: 201 }
    )
  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
