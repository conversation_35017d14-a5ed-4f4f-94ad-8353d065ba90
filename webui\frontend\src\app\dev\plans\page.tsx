'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { PlanType, PLAN_DETAILS } from '../../services/plans'
import Link from 'next/link'

export default function DevPlansPage() {
  const { currentUser, userPlan, updatePlan, isPlanLoading } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  const handlePlanChange = async (planType: PlanType) => {
    if (!currentUser) {
      setMessage({ type: 'error', text: 'You must be logged in to change plans' })
      return
    }

    setIsLoading(true)
    setMessage(null)

    try {
      await updatePlan(planType)
      setMessage({ type: 'success', text: `Successfully updated to ${planType} plan` })
    } catch (error) {
      console.error('Error updating plan:', error)
      setMessage({ type: 'error', text: 'Failed to update plan. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-900 dark:text-gray-100">
      <header className="bg-gradient-to-r from-emerald-600 to-green-600 text-white p-6 shadow-lg">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-3xl font-bold">Developer Tools</h1>
          <nav>
            <ul className="flex space-x-6">
              <li>
                <Link href="/dashboard" className="hover:text-green-200 transition-colors">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link href="/account" className="hover:text-green-200 transition-colors">
                  Account
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </header>

      <main className="container mx-auto py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 p-4 mb-8">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700 dark:text-red-200">
                  <strong>Developer Tools Only</strong> - This page is for testing purposes only and should not be accessible in production.
                </p>
              </div>
            </div>
          </div>

          <h1 className="text-3xl font-bold mb-8">Subscription Plan Testing</h1>

          {message && (
            <div className={`${message.type === 'success' ? 'bg-green-50 dark:bg-green-900/30 border-green-500' : 'bg-red-50 dark:bg-red-900/30 border-red-500'} border-l-4 p-4 mb-6`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  {message.type === 'success' ? (
                    <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm ${message.type === 'success' ? 'text-green-700 dark:text-green-200' : 'text-red-700 dark:text-red-200'}`}>
                    {message.text}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Current Plan</h2>
            {isPlanLoading ? (
              <div className="flex items-center">
                <svg className="animate-spin h-5 w-5 mr-3 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading plan information...
              </div>
            ) : userPlan ? (
              <div>
                <div className="flex items-center mb-2">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 capitalize">
                    {userPlan.planType}
                  </span>
                  <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                    Active since {userPlan.startDate.toLocaleDateString()}
                  </span>
                </div>
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Max Servers</p>
                    <p className="text-lg">{PLAN_DETAILS[userPlan.planType].maxServers === 999 ? 'Unlimited' : PLAN_DETAILS[userPlan.planType].maxServers}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Memory</p>
                    <p className="text-lg">{PLAN_DETAILS[userPlan.planType].totalMemory}GB</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Max Memory Per Server</p>
                    <p className="text-lg">{PLAN_DETAILS[userPlan.planType].maxMemoryPerServer}GB</p>
                  </div>
                </div>
              </div>
            ) : (
              <p>No plan information available</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(PLAN_DETAILS).map(([planType, details]) => (
              <div key={planType} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                <div className={`p-4 ${planType === userPlan?.planType ? 'bg-green-100 dark:bg-green-900' : 'bg-gray-50 dark:bg-gray-900'}`}>
                  <h3 className="text-lg font-semibold capitalize">{planType} Plan</h3>
                </div>
                <div className="p-6">
                  <ul className="space-y-2 mb-6">
                    {details.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handlePlanChange(planType as PlanType)}
                    disabled={isLoading || planType === userPlan?.planType}
                    className={`w-full py-2 px-4 rounded-lg font-medium ${
                      planType === userPlan?.planType
                        ? 'bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    } transition-colors`}
                  >
                    {isLoading ? 'Updating...' : planType === userPlan?.planType ? 'Current Plan' : 'Switch to This Plan'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
