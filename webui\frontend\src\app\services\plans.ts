'use client';

import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore";
import { db } from "./firebase/firebaseConfig";
import { User } from "firebase/auth";

// Define subscription plan types
export enum PlanType {
  FREE = 'free',
  BASIC = 'basic',
  PREMIUM = 'premium',
  DEVELOPER = 'developer' // Special plan for development testing
}

// Define plan limits
export interface PlanLimits {
  maxServers: number;
  maxMemoryPerServer: number; // in GB
  totalMemory: number; // in GB
  features: string[];
}

// Define plan details
export const PLAN_DETAILS: Record<PlanType, PlanLimits> = {
  [PlanType.FREE]: {
    maxServers: 1,
    maxMemoryPerServer: 2,
    totalMemory: 2,
    features: ['1 Minecraft server', '2GB RAM', 'Basic support']
  },
  [PlanType.BASIC]: {
    maxServers: 3,
    maxMemoryPerServer: 4,
    totalMemory: 8,
    features: ['3 Minecraft servers', '8GB shared RAM', 'Priority support', 'Daily backups']
  },
  [PlanType.PREMIUM]: {
    maxServers: 999, // Effectively unlimited
    maxMemoryPerServer: 16,
    totalMemory: 16,
    features: ['Unlimited servers', '16GB shared RAM', 'Premium support', 'Hourly backups', 'Custom domain']
  },
  [PlanType.DEVELOPER]: {
    maxServers: 999, // Unlimited for testing
    maxMemoryPerServer: 16,
    totalMemory: 32,
    features: ['Developer testing plan', 'No restrictions', 'For development use only']
  }
};

// Define user plan data
export interface UserPlan {
  planType: PlanType;
  startDate: Date;
  endDate?: Date; // Optional end date for subscription
  isActive: boolean;
}

// Default free plan for fallback
const DEFAULT_FREE_PLAN: UserPlan = {
  planType: PlanType.FREE,
  startDate: new Date(),
  isActive: true
};

// Get user plan from Firestore with offline support
export async function getUserPlan(userId: string): Promise<UserPlan> {
  try {
    const userRef = doc(db, "users", userId);
    const userDoc = await getDoc(userRef);

    // Check if we have a valid document and plan data
    if (userDoc.exists() && userDoc.data().plan) {
      const planData = userDoc.data().plan;

      // Handle potential missing date objects (can happen in offline mode)
      let startDate: Date;
      try {
        startDate = planData.startDate.toDate();
      } catch (error) {
        console.warn("Could not parse plan start date, using current date", error);
        startDate = new Date();
      }

      // Handle potential missing end date
      let endDate: Date | undefined = undefined;
      if (planData.endDate) {
        try {
          endDate = planData.endDate.toDate();
        } catch (error) {
          console.warn("Could not parse plan end date", error);
        }
      }

      return {
        planType: planData.planType || PlanType.FREE, // Default to FREE if missing
        startDate,
        endDate,
        isActive: planData.isActive !== false // Default to true if missing
      };
    }

    // If no plan exists, create a default FREE plan
    try {
      // Save the default plan to Firestore
      await updateDoc(userRef, {
        plan: {
          planType: DEFAULT_FREE_PLAN.planType,
          startDate: DEFAULT_FREE_PLAN.startDate,
          isActive: DEFAULT_FREE_PLAN.isActive
        }
      });

      console.log("Created default FREE plan for user", userId);
    } catch (error) {
      // If we can't update (likely offline), just return the default plan without saving
      console.warn("Could not save default plan to Firestore, likely offline:", error);
    }

    return DEFAULT_FREE_PLAN;
  } catch (error) {
    console.error("Error getting user plan:", error);
    // Return FREE plan as fallback with a warning
    console.warn("Using default FREE plan due to error. User may be offline.");
    return DEFAULT_FREE_PLAN;
  }
}

// Update user plan in Firestore with offline handling
export async function updateUserPlan(userId: string, planType: PlanType): Promise<void> {
  try {
    const userRef = doc(db, "users", userId);

    // Try to get the user document first
    try {
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        console.warn("User document does not exist, will attempt to create it");
      }
    } catch (error) {
      // If we can't get the document (possibly offline), we'll still try to update
      console.warn("Could not fetch user document, will still attempt update:", error);
    }

    const newPlan: UserPlan = {
      planType,
      startDate: new Date(),
      isActive: true
    };

    // Try to update the document
    try {
      await updateDoc(userRef, {
        plan: {
          planType: newPlan.planType,
          startDate: newPlan.startDate,
          isActive: newPlan.isActive
        }
      });
      console.log(`Successfully updated user plan to ${planType}`);
    } catch (updateError) {
      // If update fails (document might not exist), try to create it
      console.warn("Update failed, attempting to create document instead:", updateError);

      try {
        await setDoc(userRef, {
          uid: userId,
          plan: {
            planType: newPlan.planType,
            startDate: newPlan.startDate,
            isActive: newPlan.isActive
          },
          createdAt: new Date()
        });
        console.log(`Successfully created user document with ${planType} plan`);
      } catch (setError) {
        console.error("Failed to create user document:", setError);
        throw new Error(`Could not update plan: ${setError.message || 'Unknown error'}`);
      }
    }
  } catch (error) {
    console.error("Error in updateUserPlan:", error);

    // Check if it's a network error (likely offline)
    if (error.name === 'FirebaseError' &&
        (error.code === 'failed-precondition' ||
         error.code === 'unavailable' ||
         error.message.includes('offline'))) {
      throw new Error("Cannot update plan while offline. Please check your internet connection and try again.");
    }

    throw error;
  }
}

// Check if user can create a new server based on their plan with offline support
export async function canCreateServer(user: User): Promise<{allowed: boolean; reason?: string}> {
  try {
    // Get user's plan first
    let userPlan: UserPlan;
    try {
      userPlan = await getUserPlan(user.uid);
    } catch (error) {
      console.error("Error getting user plan:", error);
      // If we can't get the plan, use the default FREE plan
      userPlan = DEFAULT_FREE_PLAN;
    }

    if (!userPlan.isActive) {
      return { allowed: false, reason: "Your subscription is not active" };
    }

    const planLimits = PLAN_DETAILS[userPlan.planType];

    // Get user's current servers
    let userServers: any[] = [];
    try {
      const userRef = doc(db, "users", user.uid);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        userServers = userDoc.data().servers || [];
      } else {
        console.warn("User document does not exist, assuming no servers");
      }
    } catch (error) {
      console.warn("Error fetching user servers, assuming no servers:", error);
      // If we're offline, we'll assume no servers for safety
      if (error.message && error.message.includes('offline')) {
        return {
          allowed: false,
          reason: "Cannot check server limits while offline. Please check your internet connection and try again."
        };
      }
    }

    // Check if user has reached server limit
    if (userServers.length >= planLimits.maxServers) {
      return {
        allowed: false,
        reason: `Your ${userPlan.planType} plan allows a maximum of ${planLimits.maxServers} server(s). Please upgrade your plan to create more servers.`
      };
    }

    // Calculate total memory used
    let totalMemoryUsed = 0;
    userServers.forEach((server: any) => {
      try {
        // Extract number from memory string (e.g., "4G" -> 4)
        const serverMemory = parseInt(server.memory?.replace(/[^0-9]/g, '') || '0');
        totalMemoryUsed += isNaN(serverMemory) ? 0 : serverMemory;
      } catch (error) {
        console.warn("Error parsing server memory, ignoring:", error);
      }
    });

    // Check if adding a new server with minimum memory would exceed total memory limit
    if (totalMemoryUsed + 1 > planLimits.totalMemory) {
      return {
        allowed: false,
        reason: `Your ${userPlan.planType} plan has a total memory limit of ${planLimits.totalMemory}GB. Please upgrade your plan or remove existing servers.`
      };
    }

    return { allowed: true };
  } catch (error) {
    console.error("Error checking if user can create server:", error);

    // Check if it's a network error (likely offline)
    if (error.name === 'FirebaseError' &&
        (error.code === 'failed-precondition' ||
         error.code === 'unavailable' ||
         error.message.includes('offline'))) {
      return {
        allowed: false,
        reason: "Cannot check server limits while offline. Please check your internet connection and try again."
      };
    }

    return { allowed: false, reason: "An error occurred while checking your subscription" };
  }
}

// Get available memory options based on user's plan with offline support
export async function getAvailableMemoryOptions(user: User): Promise<string[]> {
  try {
    // Get user's plan first
    let userPlan: UserPlan;
    try {
      userPlan = await getUserPlan(user.uid);
    } catch (error) {
      console.error("Error getting user plan:", error);
      // If we can't get the plan, use the default FREE plan
      userPlan = DEFAULT_FREE_PLAN;
    }

    const planLimits = PLAN_DETAILS[userPlan.planType];

    // Get user's servers to calculate remaining memory
    let userServers: any[] = [];
    try {
      const userRef = doc(db, "users", user.uid);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        userServers = userDoc.data().servers || [];
      } else {
        console.warn("User document does not exist, assuming no servers");
      }
    } catch (error) {
      console.warn("Error fetching user servers, assuming no servers:", error);
      // If we're offline, we'll provide conservative options
      if (error.message && error.message.includes('offline')) {
        // Return conservative options for offline mode
        return ['1G', '2G'];
      }
    }

    // Calculate total memory used with error handling
    let totalMemoryUsed = 0;
    userServers.forEach((server: any) => {
      try {
        // Extract number from memory string (e.g., "4G" -> 4)
        const serverMemory = parseInt(server.memory?.replace(/[^0-9]/g, '') || '0');
        totalMemoryUsed += isNaN(serverMemory) ? 0 : serverMemory;
      } catch (error) {
        console.warn("Error parsing server memory, ignoring:", error);
      }
    });

    // Calculate remaining memory
    const remainingMemory = planLimits.totalMemory - totalMemoryUsed;

    // Generate available memory options
    const memoryOptions = ['1G', '2G', '4G', '6G', '8G', '12G', '16G'];

    // Filter options based on plan limits and remaining memory
    const filteredOptions = memoryOptions.filter(option => {
      const memoryValue = parseInt(option.replace(/[^0-9]/g, ''));
      return memoryValue <= planLimits.maxMemoryPerServer && memoryValue <= remainingMemory;
    });

    // Always ensure at least the minimum option is available
    return filteredOptions.length > 0 ? filteredOptions : ['1G'];
  } catch (error) {
    console.error("Error getting available memory options:", error);

    // Check if it's a network error (likely offline)
    if (error.name === 'FirebaseError' &&
        (error.code === 'failed-precondition' ||
         error.code === 'unavailable' ||
         error.message?.includes('offline'))) {
      console.warn("Offline detected, returning conservative memory options");
    }

    // Return basic options as fallback
    return ['1G', '2G'];
  }
}
