// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth, setPersistence, browserLocalPersistence } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyALF-98lfx1WwbqinhEILqlxazOSciycqY",
  authDomain: "minecraft-server-manager-3781c.firebaseapp.com",
  projectId: "minecraft-server-manager-3781c",
  storageBucket: "minecraft-server-manager-3781c.firebasestorage.app",
  messagingSenderId: "995040512481",
  appId: "1:995040512481:web:3249c16b15795386abeaa0",
  measurementId: "G-TCS7YEG88R"
};

const app = initializeApp(firebaseConfig);

// Initialize Firestore
const db = getFirestore(app);

// Configure Firestore settings for better offline support
// This is done automatically by the SDK in newer versions

let analytics;
if (typeof window !== "undefined") {
  analytics = getAnalytics(app);
}

const auth = getAuth(app);

// Set up authentication persistence
setPersistence(auth, browserLocalPersistence)
  .catch((error) => {
    console.error("Error setting auth persistence:", error);
  });

export {firebaseConfig, app, db, analytics, auth};
