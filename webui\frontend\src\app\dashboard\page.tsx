'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../contexts/AuthContext';
import ServerForm from '../components/ServerForm';
import ServerList from '../components/ServerList';
import WelcomeModal from '../components/WelcomeModal';
import ProtectedRoute from '../components/ProtectedRoute';

export default function Dashboard() {
  const { currentUser, logout } = useAuth();
  const router = useRouter();
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const handleSignOut = async () => {
    await logout();
  };

  useEffect(() => {
    // Check if this is the first visit
    const hasVisitedBefore = localStorage.getItem('hasVisitedBefore');
    if (!hasVisitedBefore) {
      setShowWelcomeModal(true);
      localStorage.setItem('hasVisitedBefore', 'true');
    }

    // Set loaded state after a small delay for animations
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const handleServerCreated = () => {
    // Increment the refresh trigger to cause the ServerList to refresh
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 text-gray-900 dark:text-gray-100">
      {/* Animated Header */}
      <header className="bg-gradient-to-r from-emerald-600 to-green-600 text-white p-6 shadow-lg relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="cube cube1"></div>
          <div className="cube cube2"></div>
          <div className="cube cube3"></div>
        </div>

        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center relative z-10">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm3 1h6v6H6V5z" clipRule="evenodd" />
              <path d="M6 5H5v1h1V5z" />
              <path d="M5 8h1v1H5V8z" />
              <path d="M8 5h1v1H8V5z" />
              <path d="M8 8h1v1H8V8z" />
              <path d="M11 5h1v1h-1V5z" />
              <path d="M11 8h1v1h-1V8z" />
              <path d="M5 11h1v1H5v-1z" />
              <path d="M8 11h1v1H8v-1z" />
              <path d="M11 11h1v1h-1v-1z" />
            </svg>
            <div>
              <h1 className="text-3xl font-bold">Minecraft Server Manager</h1>
              <p className="text-green-100">Manage your Minecraft servers with ease</p>
            </div>
          </div>

          <div className="mt-4 md:mt-0 flex space-x-4 items-center">
            <a href="/" className="bg-white text-green-600 hover:bg-gray-100 px-4 py-2 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-300 inline-flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              Home
            </a>

            <Link href="/account" className="bg-white text-green-600 hover:bg-gray-100 px-4 py-2 rounded-lg font-medium shadow-md hover:shadow-lg transition-all duration-300 inline-flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              Account
            </Link>
          </div>
        </div>
      </header>

      <main className="container mx-auto py-10 px-4 max-w-7xl">
        <div className="mb-8">
          <div className="bg-green-50 dark:bg-green-900 border-l-4 border-green-500 p-4 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-2xl">👋</span>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-green-800 dark:text-green-200">
                  Welcome, {currentUser?.displayName || currentUser?.email?.split('@')[0] || 'User'}!
                </h3>
                <p className="mt-1 text-sm text-green-700 dark:text-green-300">
                  Manage your Minecraft servers from this dashboard. Need help? Check out our documentation.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {/* Server Creation Form with Animation */}
          <div className={`lg:col-span-1 transition-all duration-700 transform ${isLoaded ? 'translate-x-0 opacity-100' : '-translate-x-10 opacity-0'}`}>
            <div className="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-shadow duration-300">
              <div className="px-8 py-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h2 className="text-2xl font-bold flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  Create New Server
                </h2>
              </div>
              <div className="p-6">
                <ServerForm onServerCreated={handleServerCreated} />
              </div>
            </div>
          </div>

          {/* Server List with Animation */}
          <div className={`lg:col-span-2 transition-all duration-700 transform ${isLoaded ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'} delay-150`}>
            <div className="bg-white dark:bg-gray-800 shadow-xl rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-2xl transition-shadow duration-300">
              <div className="px-8 py-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <h2 className="text-2xl font-bold flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                  </svg>
                  Your Minecraft Servers
                </h2>
              </div>
              <div className="p-6">
                <ServerList refreshTrigger={refreshTrigger} />
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-800 text-gray-300 p-6 mt-12">
        <div className="container mx-auto text-center">
          <p className="text-sm">Minecraft Server Manager &copy; {new Date().getFullYear()}</p>
          <p className="text-xs mt-2 text-gray-400">Manage your Minecraft servers with ease</p>
        </div>
      </footer>

      {/* Welcome Modal */}
      {showWelcomeModal && (
        <WelcomeModal onClose={() => setShowWelcomeModal(false)} />
      )}
    </div>
    </ProtectedRoute>
  );
}
