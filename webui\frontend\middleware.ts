import { auth } from './auth'
 
// This function can be marked `async` if using `await` inside
export default auth((req) => {
  // req.auth is the session object
  // If the user is not signed in and is trying to access a protected route,
  // redirect them to the sign-in page
  const { nextUrl } = req
  const isLoggedIn = !!req.auth
  
  // Define protected routes
  const isProtectedRoute = nextUrl.pathname.startsWith('/dashboard')
  
  if (isProtectedRoute && !isLoggedIn) {
    return Response.redirect(new URL('/signin', nextUrl))
  }
  
  // If the user is signed in and trying to access auth pages, redirect to dashboard
  const isAuthRoute = 
    nextUrl.pathname.startsWith('/signin') || 
    nextUrl.pathname.startsWith('/signup')
  
  if (isAuthRoute && isLoggedIn) {
    return Response.redirect(new URL('/dashboard', nextUrl))
  }
  
  return null
})

// Optionally, don't invoke Middleware on some paths
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
}
