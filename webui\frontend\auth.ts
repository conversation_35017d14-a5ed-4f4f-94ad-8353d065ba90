import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"

// This is a simple in-memory store for demo purposes
// In a real app, you would use a database
const users = new Map<string, { id: string, name: string, email: string, password: string }>()

// Add a demo user
users.set("<EMAIL>", {
  id: "1",
  name: "Demo User",
  email: "<EMAIL>",
  password: "password123" // In a real app, this would be hashed
})

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Credentials({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // The credentials is used to generate a suitable form on the sign in page.
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // Validate the credentials
        const { email, password } = credentials as {
          email: string,
          password: string
        }

        // Simple validation
        if (!email || !email.includes('@') || !password || password.length < 6) {
          console.log("Invalid credentials format")
          return null
        }

        const user = users.get(email)

        if (!user) {
          console.log("User not found")
          return null
        }

        // In a real app, you would check the password against a hashed version
        const passwordsMatch = user.password === password

        if (passwordsMatch) return user

        console.log("Invalid credentials")
        return null
      }
    })
  ],
  pages: {
    signIn: '/signin',
  },
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user
      const isOnDashboard = nextUrl.pathname.startsWith('/dashboard')
      if (isOnDashboard) {
        if (isLoggedIn) return true
        return false // Redirect unauthenticated users to login page
      } else if (isLoggedIn) {
        return true
      }
      return true
    },
    jwt({ token, user }) {
      if (user) {
        token.id = user.id
      }
      return token
    },
    session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string
      }
      return session
    },
  },
  session: { strategy: "jwt" },
})