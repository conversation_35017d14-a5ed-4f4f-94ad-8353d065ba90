'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, updateProfile } from 'firebase/auth'
import { subscribeToAuthChanges, signOutUser } from '../services/firebase/authService'
import { useRouter } from 'next/navigation'
import { PlanType, UserPlan, getUserPlan, updateUserPlan } from '../services/plans'

interface AuthContextType {
  currentUser: User | null
  isLoading: boolean
  userPlan: UserPlan | null
  isPlanLoading: boolean
  logout: () => Promise<void>
  updateUserProfile: (displayName: string) => Promise<void>
  updatePlan: (planType: PlanType) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [userPlan, setUserPlan] = useState<UserPlan | null>(null)
  const [isPlanLoading, setIsPlanLoading] = useState(true)
  const router = useRouter()

  // Fetch user plan when user changes
  useEffect(() => {
    const fetchUserPlan = async () => {
      if (currentUser) {
        setIsPlanLoading(true)
        try {
          const plan = await getUserPlan(currentUser.uid)
          setUserPlan(plan)
        } catch (error) {
          console.error('Error fetching user plan:', error)
        } finally {
          setIsPlanLoading(false)
        }
      } else {
        setUserPlan(null)
        setIsPlanLoading(false)
      }
    }

    fetchUserPlan()
  }, [currentUser])

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = subscribeToAuthChanges((user) => {
      setCurrentUser(user)
      setIsLoading(false)
    })

    // Cleanup subscription on unmount
    return () => unsubscribe()
  }, [])

  const logout = async () => {
    try {
      await signOutUser()
      router.push('/auth')
    } catch (error) {
      console.error('Error logging out:', error)
    }
  }

  const updateUserProfile = async (displayName: string) => {
    if (!currentUser) {
      throw new Error('No user is currently logged in')
    }

    try {
      await updateProfile(currentUser, {
        displayName
      })

      // Force a refresh of the user object
      setCurrentUser({ ...currentUser })
    } catch (error) {
      console.error('Error updating profile:', error)
      throw error
    }
  }

  const updatePlan = async (planType: PlanType) => {
    if (!currentUser) {
      throw new Error('No user is currently logged in')
    }

    try {
      await updateUserPlan(currentUser.uid, planType)

      // Refresh the user plan
      const updatedPlan = await getUserPlan(currentUser.uid)
      setUserPlan(updatedPlan)
    } catch (error) {
      console.error('Error updating user plan:', error)
      throw error
    }
  }

  const value = {
    currentUser,
    isLoading,
    userPlan,
    isPlanLoading,
    logout,
    updateUserProfile,
    updatePlan
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
